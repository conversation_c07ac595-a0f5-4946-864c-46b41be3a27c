<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清理验证测试 - 多订单模式</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        button {
            background: #9F299F;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #7a1f7a; }
        
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧹 清理验证测试 - 多订单模式</h1>
    
    <div class="test-container">
        <h2>📋 测试目标</h2>
        <ul>
            <li>✅ 验证控制台不再出现文件缺失错误</li>
            <li>✅ 验证多订单功能正常工作</li>
            <li>✅ 验证样式显示正常（内联样式生效）</li>
            <li>✅ 验证modules/multi-order.js正常加载和工作</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>🔍 文件加载状态检查</h2>
        <div id="loading-status"></div>
        <button onclick="checkLoadingStatus()">检查文件加载状态</button>
    </div>

    <div class="test-container">
        <h2>🚀 多订单功能测试</h2>
        <textarea id="test-input" placeholder="输入多订单测试文本...">
订单1: 张三 2024-08-20 10:00 北京首都机场 → 王府井酒店
订单2: 李四 2024-08-20 14:00 上海浦东机场 → 外滩酒店  
订单3: 王五 2024-08-20 18:00 广州白云机场 → 珠江新城酒店
        </textarea>
        <div>
            <button onclick="testDetection()">测试检测功能</button>
            <button onclick="testFullWorkflow()">测试完整流程</button>
            <button onclick="testUIDisplay()">测试UI显示</button>
        </div>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>📊 控制台输出监控</h2>
        <div class="console-output" id="console-output"></div>
        <button onclick="clearConsole()">清空控制台</button>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/core/logger.js"></script>
    <script src="modules/multi-order.js"></script>
    
    <script>
        // 控制台输出捕获
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        // 测试函数
        function showResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            results.innerHTML += `<div class="status ${type}">${message}</div>`;
        }
        
        function checkLoadingStatus() {
            const status = document.getElementById('loading-status');
            status.innerHTML = '';
            
            // 检查关键对象是否存在
            const checks = [
                { name: 'MultiOrder模块', check: () => typeof MultiOrder !== 'undefined' },
                { name: 'MultiOrder.detect', check: () => typeof MultiOrder?.detect === 'function' },
                { name: 'MultiOrder.process', check: () => typeof MultiOrder?.process === 'function' },
                { name: 'MultiOrder.showUI', check: () => typeof MultiOrder?.showUI === 'function' },
                { name: 'MultiOrder.saveToHistory', check: () => typeof MultiOrder?.saveToHistory === 'function' },
                { name: 'window.ota对象', check: () => typeof window.ota !== 'undefined' },
                { name: 'OTA.adapters', check: () => typeof window.OTA?.adapters !== 'undefined' }
            ];
            
            checks.forEach(({ name, check }) => {
                const passed = check();
                const statusClass = passed ? 'success' : 'error';
                const icon = passed ? '✅' : '❌';
                status.innerHTML += `<div class="status ${statusClass}">${icon} ${name}: ${passed ? '正常' : '缺失'}</div>`;
            });
        }
        
        async function testDetection() {
            if (typeof MultiOrder === 'undefined') {
                return showResult('MultiOrder模块未加载', 'error');
            }
            
            const content = document.getElementById('test-input').value;
            try {
                const result = MultiOrder.detect(content);
                showResult(`检测结果: ${result.isMultiOrder ? '多订单' : '单订单'} (置信度: ${result.confidence}, 订单数: ${result.orderCount})`, 
                          result.isMultiOrder ? 'success' : 'warning');
            } catch (error) {
                showResult(`检测失败: ${error.message}`, 'error');
            }
        }
        
        async function testFullWorkflow() {
            if (typeof MultiOrder === 'undefined') {
                return showResult('MultiOrder模块未加载', 'error');
            }
            
            const content = document.getElementById('test-input').value;
            try {
                // 完整工作流程
                const detection = MultiOrder.detect(content);
                if (!detection.isMultiOrder) {
                    return showResult('未检测到多订单', 'warning');
                }
                
                const processed = MultiOrder.process(detection.orders);
                showResult(`处理完成: ${processed.length}个订单已处理`, 'success');
                
                // 测试历史保存（模拟）
                await MultiOrder.saveToHistory(processed);
                showResult('历史保存测试完成', 'success');
                
            } catch (error) {
                showResult(`完整流程测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testUIDisplay() {
            if (typeof MultiOrder === 'undefined') {
                return showResult('MultiOrder模块未加载', 'error');
            }
            
            const content = document.getElementById('test-input').value;
            try {
                const detection = MultiOrder.detect(content);
                if (!detection.isMultiOrder) {
                    return showResult('未检测到多订单，无法测试UI', 'warning');
                }
                
                const processed = MultiOrder.process(detection.orders);
                const uiResult = MultiOrder.showUI(processed);
                
                showResult(`UI显示测试: ${uiResult ? '成功' : '失败'}`, uiResult ? 'success' : 'error');
                
                // 检查UI元素是否创建
                setTimeout(() => {
                    const container = document.querySelector('.multi-order-module');
                    if (container) {
                        showResult('UI容器已创建，样式应该正常显示', 'success');
                    } else {
                        showResult('UI容器未找到', 'warning');
                    }
                }, 100);
                
            } catch (error) {
                showResult(`UI显示测试失败: ${error.message}`, 'error');
            }
        }
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkLoadingStatus();
                console.log('🧹 清理验证测试页面已加载');
            }, 1000);
        });
    </script>
</body>
</html>
