<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Order Module Test - Clean Version</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        textarea { width: 100%; height: 100px; padding: 8px; margin: 8px 0; }
        button { background: #007bff; color: white; border: none; padding: 8px 16px; margin: 4px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
        .result.success { background: #d4edda; color: #155724; }
        .result.error { background: #f8d7da; color: #721c24; }
        .status { padding: 8px; margin: 8px 0; border-radius: 4px; font-weight: bold; }
        .status.ready { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Multi-Order Module - Clean Test</h1>
        <div id="status" class="status ready">Ready to test</div>
        
        <div class="test-section">
            <h3>Test Multi-Order Detection & Processing</h3>
            <textarea id="test-input" placeholder="Enter multi-order content...">Customer: John Doe
Order 1: 2024-01-15 10:00 Airport to Hotel
Order 2: 2024-01-16 14:00 Hotel to Mall  
Order 3: 2024-01-17 08:00 Hotel to Airport
Phone: +1234567890</textarea>
            <div>
                <button onclick="testDetect()">Detect</button>
                <button onclick="testProcess()">Process</button>
                <button onclick="testUI()">Show UI</button>
                <button onclick="testFull()">Full Test</button>
            </div>
            <div id="result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Integration Test</h3>
            <button onclick="testResultProcessor()">Test Gemini Integration</button>
            <button onclick="testHistory()">Test History Save</button>
            <div id="integration-result" class="result"></div>
        </div>
    </div>

    <script type="module">
        let MultiOrder;

        // Load the module
        async function init() {
            try {
                MultiOrder = (await import('./modules/multi-order.js')).default;
                document.getElementById('status').textContent = 'Module loaded successfully';
                console.log('MultiOrder module loaded:', MultiOrder);
            } catch (error) {
                document.getElementById('status').textContent = `Load failed: ${error.message}`;
                console.error('Load error:', error);
            }
        }

        // Test functions
        window.testDetect = async function() {
            if (!MultiOrder) return showResult('Module not loaded', 'error');
            
            try {
                const content = document.getElementById('test-input').value;
                const result = await MultiOrder.detect(content);
                showResult(`Detection result:\n${JSON.stringify(result, null, 2)}`, 'success');
            } catch (error) {
                showResult(`Detection failed: ${error.message}`, 'error');
            }
        };

        window.testProcess = async function() {
            if (!MultiOrder) return showResult('Module not loaded', 'error');
            
            try {
                const content = document.getElementById('test-input').value;
                const detection = await MultiOrder.detect(content);
                
                if (!detection.isMultiOrder) {
                    return showResult('No multi-order detected', 'error');
                }
                
                const processed = MultiOrder.process(detection.orders);
                showResult(`Processing result:\n${JSON.stringify(processed, null, 2)}`, 'success');
            } catch (error) {
                showResult(`Processing failed: ${error.message}`, 'error');
            }
        };

        window.testUI = async function() {
            if (!MultiOrder) return showResult('Module not loaded', 'error');
            
            try {
                const content = document.getElementById('test-input').value;
                const detection = await MultiOrder.detect(content);
                
                if (!detection.isMultiOrder) {
                    return showResult('No multi-order detected', 'error');
                }
                
                const processed = MultiOrder.process(detection.orders);
                MultiOrder.showUI(processed);
                showResult('UI displayed successfully', 'success');
            } catch (error) {
                showResult(`UI failed: ${error.message}`, 'error');
            }
        };

        window.testFull = async function() {
            if (!MultiOrder) return showResult('Module not loaded', 'error');
            
            try {
                const content = document.getElementById('test-input').value;
                
                // Full workflow
                const detection = await MultiOrder.detect(content);
                if (!detection.isMultiOrder) {
                    return showResult('Full test failed: No multi-order detected', 'error');
                }
                
                const processed = MultiOrder.process(detection.orders);
                MultiOrder.showUI(processed);
                await MultiOrder.saveToHistory(processed);
                
                showResult(`Full workflow completed:
- Detected: ${detection.orderCount} orders
- Processed: ${processed.length} orders  
- UI: Displayed
- History: Saved

State: ${JSON.stringify(MultiOrder.getState(), null, 2)}`, 'success');
                
            } catch (error) {
                showResult(`Full test failed: ${error.message}`, 'error');
            }
        };

        window.testResultProcessor = async function() {
            // Simulate Gemini result processing
            try {
                const mockGeminiResult = {
                    orders: [
                        { customer_name: 'Test User', ota_price: '100', pickup_location: 'Airport' },
                        { customer_name: 'Test User', ota_price: '150', pickup_location: 'Hotel' }
                    ],
                    confidence: 0.9
                };

                // Import result processor if available
                if (window.OTA && window.OTA.ResultProcessor) {
                    const processor = new window.OTA.ResultProcessor();
                    await processor.triggerMultiOrderMode(mockGeminiResult.orders, mockGeminiResult);
                    showIntegrationResult('Gemini integration test passed', 'success');
                } else {
                    // Direct test
                    const processed = MultiOrder.process(mockGeminiResult.orders);
                    MultiOrder.showUI(processed);
                    showIntegrationResult('Direct integration test passed', 'success');
                }
            } catch (error) {
                showIntegrationResult(`Integration test failed: ${error.message}`, 'error');
            }
        };

        window.testHistory = async function() {
            if (!MultiOrder) return showIntegrationResult('Module not loaded', 'error');
            
            try {
                const mockOrders = [
                    { id: 'test1', customer_name: 'History Test', status: 'completed' }
                ];
                
                const result = await MultiOrder.saveToHistory(mockOrders);
                showIntegrationResult(`History save: ${result ? 'Success' : 'Failed'}`, result ? 'success' : 'error');
            } catch (error) {
                showIntegrationResult(`History test failed: ${error.message}`, 'error');
            }
        };

        // Utility functions
        function showResult(message, type) {
            const el = document.getElementById('result');
            el.textContent = message;
            el.className = `result ${type}`;
        }

        function showIntegrationResult(message, type) {
            const el = document.getElementById('integration-result');
            el.textContent = message;
            el.className = `result ${type}`;
        }

        // Initialize on load
        init();
    </script>
</body>
</html>